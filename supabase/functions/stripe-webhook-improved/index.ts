import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import Strip<PERSON> from "https://esm.sh/stripe@15.6.0?target=deno";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.49.1";

// CORS headers to allow cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, stripe-signature',
  'Access-Control-Allow-Methods': 'POST, OPTIONS'
};

// Create a new instance of Strip<PERSON> for each request to avoid potential issues with shared state
function createStripeInstance(secretKey: string): Stripe {
  return new Stripe(secretKey, {
    apiVersion: '2024-11-20',
  });
}

// Helper function to validate UUID format
function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

// Helper function to update certificate payment status
async function updateCertificatePaymentStatus(
  supabase: any,
  certificateId: string,
  paymentStatus: string,
  sessionId: string,
  requestId: string
): Promise<{ success: boolean; orderNumber?: string; error?: string }> {
  try {
    // First, get the current certificate to check if order_number exists
    const { data: currentCert, error: fetchError } = await supabase
      .from('energieausweise')
      .select('order_number, payment_status')
      .eq('id', certificateId)
      .single();

    if (fetchError) {
      console.error(`❌ Error fetching certificate ${certificateId} [${requestId}]:`, fetchError);
      return { success: false, error: `Failed to fetch certificate: ${fetchError.message}` };
    }

    // Check if payment was already processed to prevent double processing
    if (paymentStatus === 'paid' && currentCert?.payment_status === 'paid') {
      console.log(`⚠️ Certificate ${certificateId} is already marked as paid - skipping duplicate processing [${requestId}]`);
      return { success: true, orderNumber: currentCert.order_number };
    }

    // Generate order_number if it doesn't exist and payment is successful
    const orderNumber = currentCert?.order_number ||
      (paymentStatus === 'paid' ? `EA-${certificateId.slice(-8).toUpperCase()}` : undefined);

    // Update payment status in database
    const updateData: any = {
      payment_status: paymentStatus,
      stripe_checkout_session_id: sessionId,
      updated_at: new Date().toISOString(),
    };

    if (orderNumber) {
      updateData.order_number = orderNumber;
    }

    const { error: updateError } = await supabase
      .from('energieausweise')
      .update(updateData)
      .eq('id', certificateId);

    if (updateError) {
      console.error(`❌ Error updating payment status for certificate ${certificateId} [${requestId}]:`, updateError);
      return { success: false, error: `Failed to update payment status: ${updateError.message}` };
    }

    console.log(`✅ Successfully updated payment status to '${paymentStatus}' for certificate: ${certificateId} [${requestId}]`);
    if (orderNumber) {
      console.log(`✅ Order number set to: ${orderNumber} [${requestId}]`);
    }

    return { success: true, orderNumber };

  } catch (error) {
    console.error(`❌ Unexpected error updating certificate ${certificateId} [${requestId}]:`, error);
    return { success: false, error: error.message || 'Unexpected error' };
  }
}

serve(async (req) => {
  // Generate unique request ID for tracking
  const requestId = crypto.randomUUID();
  const startTime = Date.now();
  let supabase: any = null;

  console.log(`\n🚀 === WEBHOOK REQUEST START [${requestId}] ===`);
  console.log(`Request method: ${req.method}`);
  console.log(`Request URL: ${req.url}`);
  console.log(`Timestamp: ${new Date().toISOString()}`);

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    console.log(`✅ CORS preflight handled [${requestId}]`);
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }

  // Validate request method
  if (req.method !== 'POST') {
    console.log(`❌ Invalid method ${req.method} [${requestId}]`);
    return new Response(
      JSON.stringify({ error: 'Method not allowed', allowed_methods: ['POST'] }),
      { status: 405, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }

  try {
    // Validate environment variables
    const stripeSecretKey = Deno.env.get("STRIPE_SECRET_KEY");
    const webhookSecret = Deno.env.get("STRIPE_WEBHOOK_SECRET");
    const supabaseUrl = Deno.env.get("SUPABASE_URL");
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");

    const missingVars = [];
    if (!stripeSecretKey) missingVars.push("STRIPE_SECRET_KEY");
    if (!webhookSecret) missingVars.push("STRIPE_WEBHOOK_SECRET");
    if (!supabaseUrl) missingVars.push("SUPABASE_URL");
    if (!supabaseServiceKey) missingVars.push("SUPABASE_SERVICE_ROLE_KEY");

    if (missingVars.length > 0) {
      console.error(`❌ Missing environment variables: ${missingVars.join(", ")} [${requestId}]`);
      return new Response(
        JSON.stringify({
          error: "Configuration Error",
          message: `Missing required environment variables: ${missingVars.join(", ")}`,
          request_id: requestId
        }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Get signature from headers
    const signature = req.headers.get("stripe-signature");
    console.log(`Stripe signature header present: ${signature ? "YES" : "NO"} [${requestId}]`);

    if (!signature) {
      console.error(`❌ Missing Stripe signature header [${requestId}]`);
      return new Response(
        JSON.stringify({
          error: "Missing Stripe signature",
          message: "Webhook signature verification failed - no signature header found",
          request_id: requestId
        }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Log signature components for debugging
    try {
      const components = signature.split(',').reduce((acc, part) => {
        const [key, value] = part.split('=');
        acc[key] = value;
        return acc;
      }, {} as Record<string, string>);

      console.log(`Signature components [${requestId}]:`, components);
    } catch (err) {
      console.log(`Failed to parse signature components [${requestId}]:`, err.message);
    }

    // Initialize Supabase client
    supabase = createClient(supabaseUrl, supabaseServiceKey);
    console.log(`✅ Supabase client initialized [${requestId}]`);

    // IMPORTANT: Clone the request to ensure we're working with a fresh body
    // This prevents issues with the body stream being consumed multiple times
    const clonedRequest = req.clone();
    
    // Get the raw body for signature verification
    console.log(`📥 Reading request body [${requestId}]`);
    const body = await clonedRequest.text();

    console.log(`Body length: ${body.length} characters [${requestId}]`);
    console.log(`Body preview: ${body.substring(0, 100)}... [${requestId}]`);
    
    // Log the first few bytes of the body to check for BOM or encoding issues
    const bodyBytes = new TextEncoder().encode(body).slice(0, 10);
    console.log(`Body first 10 bytes [${requestId}]:`, Array.from(bodyBytes));

    // Initialize Stripe with secret key - create a fresh instance for each request
    console.log(`🔧 Initializing Stripe client [${requestId}]`);
    const stripe = createStripeInstance(stripeSecretKey);

    // Create SubtleCryptoProvider for async webhook verification in Deno
    const cryptoProvider = Stripe.createSubtleCryptoProvider();

    // Attempt signature verification using Stripe's recommended method for Deno
    console.log(`🔐 Attempting Stripe signature verification [${requestId}]`);
    let event: Stripe.Event;
    
    try {
      // Log the exact inputs we're passing to constructEventAsync for debugging
      console.log(`Verification inputs [${requestId}]:`);
      console.log(`- Body type: ${typeof body}`);
      console.log(`- Signature type: ${typeof signature}`);
      console.log(`- Secret type: ${typeof webhookSecret}`);
      console.log(`- Secret length: ${webhookSecret.length}`);
      
      event = await stripe.webhooks.constructEventAsync(
        body,
        signature,
        webhookSecret,
        undefined,
        cryptoProvider
      );

      console.log(`✅ Signature verification successful [${requestId}]!`);
      console.log(`✅ Verified event type: ${event.type} [${requestId}]`);
      console.log(`✅ Verified event ID: ${event.id} [${requestId}]`);
    } catch (err) {
      console.error(`❌ Signature verification failed [${requestId}]:`, err.message);
      
      // Log detailed debugging information
      console.error(`❌ Request headers [${requestId}]:`, Object.fromEntries(req.headers.entries()));
      console.error(`❌ Body length [${requestId}]: ${body.length}`);
      console.error(`❌ Webhook secret length [${requestId}]: ${webhookSecret.length}`);
      console.error(`❌ Signature format [${requestId}]: ${signature}`);
      
      // Try to parse the body as JSON to check if it's valid
      try {
        const jsonBody = JSON.parse(body);
        console.log(`Body is valid JSON [${requestId}]: ${jsonBody.type || 'unknown type'}`);
      } catch (jsonErr) {
        console.error(`Body is not valid JSON [${requestId}]: ${jsonErr.message}`);
      }
      
      return new Response(
        JSON.stringify({
          error: "Webhook signature verification failed",
          message: err.message,
          details: "Payment processing aborted due to failed signature verification",
          request_id: requestId,
          timestamp: new Date().toISOString(),
          debug_info: {
            body_length: body.length,
            signature_present: !!signature,
            webhook_secret_length: webhookSecret.length,
            error: err.message
          }
        }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // At this point, the signature is verified and we can process the event
    console.log(`\n=== PROCESSING VERIFIED EVENT [${requestId}] ===`);
    console.log(`Event type: ${event.type} [${requestId}]`);
    console.log(`Event ID: ${event.id} [${requestId}]`);
    console.log(`Event created: ${new Date(event.created * 1000).toISOString()} [${requestId}]`);

    // Process different event types
    if (event.type === 'checkout.session.completed') {
      const session = event.data.object as Stripe.Checkout.Session;
      console.log(`✅ Checkout session completed [${requestId}]: ${session.id}`);
      
      // Extract certificate ID from metadata
      const certificateId = session.metadata?.certificate_id;
      
      if (!certificateId) {
        console.error(`❌ Missing certificate_id in session metadata [${requestId}]`);
        return new Response(
          JSON.stringify({
            error: "Missing certificate ID",
            message: "The checkout session does not contain a certificate_id in metadata",
            request_id: requestId
          }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }

      // Validate certificate ID format
      if (!isValidUUID(certificateId)) {
        console.error(`❌ Invalid certificate ID format [${requestId}]: ${certificateId}`);
        return new Response(
          JSON.stringify({
            error: "Invalid certificate ID format",
            message: "The certificate_id in metadata is not a valid UUID",
            request_id: requestId
          }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }

      // Update certificate payment status
      const updateResult = await updateCertificatePaymentStatus(
        supabase,
        certificateId,
        'paid',
        session.id,
        requestId
      );

      if (!updateResult.success) {
        console.error(`❌ Failed to update certificate payment status [${requestId}]:`, updateResult.error);
        return new Response(
          JSON.stringify({
            error: "Database update failed",
            message: updateResult.error,
            request_id: requestId
          }),
          { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }

      console.log(`✅ Certificate ${certificateId} payment status updated to 'paid' [${requestId}]`);
      console.log(`✅ Order number: ${updateResult.orderNumber} [${requestId}]`);
    } 
    else if (event.type === 'checkout.session.expired') {
      const session = event.data.object as Stripe.Checkout.Session;
      console.log(`⚠️ Checkout session expired [${requestId}]: ${session.id}`);
      
      // Extract certificate ID from metadata
      const certificateId = session.metadata?.certificate_id;
      
      if (certificateId && isValidUUID(certificateId)) {
        // Update certificate payment status to expired
        const updateResult = await updateCertificatePaymentStatus(
          supabase,
          certificateId,
          'expired',
          session.id,
          requestId
        );

        if (!updateResult.success) {
          console.error(`❌ Failed to update certificate payment status to expired [${requestId}]:`, updateResult.error);
        } else {
          console.log(`✅ Certificate ${certificateId} payment status updated to 'expired' [${requestId}]`);
        }
      } else {
        console.log(`⚠️ No valid certificate ID found in expired session [${requestId}]`);
      }
    }
    else {
      // Log other event types but don't process them
      console.log(`ℹ️ Unhandled event type [${requestId}]: ${event.type}`);
    }

    // Calculate processing time
    const processingTime = Date.now() - startTime;
    console.log(`✅ Event processed successfully [${requestId}]`);
    console.log(`✅ Processing time: ${processingTime}ms [${requestId}]`);
    console.log(`🏁 === WEBHOOK REQUEST END [${requestId}] ===\n`);

    // Return success response
    return new Response(
      JSON.stringify({
        received: true,
        event_type: event.type,
        event_id: event.id,
        request_id: requestId,
        processing_time_ms: processingTime,
        processed_at: new Date().toISOString()
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    const processingTime = Date.now() - startTime;
    console.error(`❌ Error processing webhook [${requestId}]:`, error);
    console.error(`❌ Processing time [${requestId}]: ${processingTime}ms`);
    console.error(`🏁 === WEBHOOK REQUEST END (ERROR) [${requestId}] ===\n`);

    return new Response(
      JSON.stringify({
        error: "Webhook Processing Error",
        message: error.message,
        request_id: requestId,
        processing_time_ms: processingTime,
        timestamp: new Date().toISOString(),
        stack: error.stack
      }),
      {
        status: 500,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      }
    );
  }
});