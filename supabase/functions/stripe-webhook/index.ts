import Stripe from "https://esm.sh/stripe@15.6.0?target=deno";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.49.1";
import { Resend } from "https://esm.sh/resend@2.1.0";

// CORS headers to allow cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, stripe-signature',
  'Access-Control-Allow-Methods': 'POST, OPTIONS'
};

/**
 * Create a new Stripe instance with the provided secret key
 * This ensures a fresh instance for each request to avoid shared state issues
 */
function createStripeInstance(secretKey: string): Stripe {
  return new Stripe(secretKey, {
    apiVersion: '2024-11-20',
  });
}

// Email types
type EmailType = 'customer_success' | 'customer_failure' | 'admin_success' | 'admin_failure';

// Email status
type EmailStatus = 'sent' | 'failed' | 'pending';

// Email log interface
interface EmailLog {
  id?: string;
  certificate_id: string;
  recipient_email: string;
  email_type: EmailType;
  sent_at?: string;
  status: EmailStatus;
  error_message?: string;
  resend_message_id?: string;
}

// Certificate data interface
interface CertificateData {
  id: string;
  order_number: string;
  certificate_type: string;
  payment_status: string;
  user_id: string;
  objektdaten?: any;
  gebaeudedetails1?: any;
  created_at: string;
}

// User data interface
interface UserData {
  id: string;
  email: string;
  full_name?: string;
}

// Email service class - FIXED: No longer creates its own Supabase client
class EmailService {
  private resend: Resend;
  private supabase: any;
  private fromEmail: string;
  private adminEmail: string;

  constructor(supabaseClient: any) {
    const resendApiKey = Deno.env.get("RESEND_API_KEY");
    this.fromEmail = Deno.env.get("FROM_EMAIL");
    this.adminEmail = Deno.env.get("ADMIN_EMAIL");

    if (!resendApiKey) {
      throw new Error("RESEND_API_KEY environment variable is required");
    }
    if (!this.fromEmail) {
      throw new Error("FROM_EMAIL environment variable is required");
    }
    if (!this.adminEmail) {
      throw new Error("ADMIN_EMAIL environment variable is required");
    }

    // Use the shared Supabase client instead of creating a new one
    this.resend = new Resend(resendApiKey);
    this.supabase = supabaseClient;
  }

  // Log email attempt to database
  private async logEmail(emailLog: EmailLog): Promise<string | null> {
    try {
      const { data, error } = await this.supabase
        .from('email_logs')
        .insert(emailLog)
        .select('id')
        .single();

      if (error) {
        console.error('Error logging email:', error);
        return null;
      }

      return data?.id || null;
    } catch (error) {
      console.error('Error logging email:', error);
      return null;
    }
  }

  // Update email log status
  private async updateEmailLog(logId: string, updates: Partial<EmailLog>): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('email_logs')
        .update(updates)
        .eq('id', logId);

      if (error) {
        console.error('Error updating email log:', error);
      }
    } catch (error) {
      console.error('Error updating email log:', error);
    }
  }

  // Get user email from auth.users table
  private async getUserEmail(userId: string): Promise<string | null> {
    try {
      const { data, error } = await this.supabase.auth.admin.getUserById(userId);

      if (error || !data?.user?.email) {
        console.error('Error fetching user email:', error);
        return null;
      }

      return data.user.email;
    } catch (error) {
      console.error('Error fetching user email:', error);
      return null;
    }
  }

  // Get certificate and user data
  private async getCertificateData(certificateId: string): Promise<{ certificate: CertificateData; userEmail: string } | null> {
    try {
      const { data: certificate, error: certError } = await this.supabase
        .from('energieausweise')
        .select('*')
        .eq('id', certificateId)
        .single();

      if (certError || !certificate) {
        console.error('Error fetching certificate:', certError);
        return null;
      }

      const userEmail = await this.getUserEmail(certificate.user_id);
      if (!userEmail) {
        console.error('Could not fetch user email for certificate:', certificateId);
        return null;
      }

      return { certificate, userEmail };
    } catch (error) {
      console.error('Error fetching certificate data:', error);
      return null;
    }
  }

  // Generate customer success email content
  private generateCustomerSuccessEmail(certificate: CertificateData, userEmail: string): { subject: string; html: string } {
    const certificateTypeMap: Record<string, string> = {
      'WG/V': 'Wohngebäude Verbrauchsausweis',
      'WG/B': 'Wohngebäude Bedarfsausweis',
      'NWG/V': 'Nichtwohngebäude Verbrauchsausweis'
    };

    const certificateTypeName = certificateTypeMap[certificate.certificate_type] || certificate.certificate_type;
    const buildingAddress = certificate.objektdaten?.strasse && certificate.objektdaten?.hausnummer
      ? `${certificate.objektdaten.strasse} ${certificate.objektdaten.hausnummer}, ${certificate.objektdaten.plz} ${certificate.objektdaten.ort}`
      : 'Ihre Immobilie';

    const subject = `Zahlungsbestätigung - Energieausweis ${certificate.order_number}`;

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Zahlungsbestätigung</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #16a34a; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Zahlungsbestätigung</h1>
          </div>
          <div class="content">
            <p>Vielen Dank für Ihre Bestellung! Ihre Zahlung wurde erfolgreich verarbeitet.</p>

            <div class="details">
              <h3>Bestelldetails:</h3>
              <p><strong>Bestellnummer:</strong> ${certificate.order_number}</p>
              <p><strong>Energieausweis-Typ:</strong> ${certificateTypeName}</p>
              <p><strong>Immobilie:</strong> ${buildingAddress}</p>
              <p><strong>Bestelldatum:</strong> ${new Date(certificate.created_at).toLocaleDateString('de-DE')}</p>
            </div>

            <p>Ihr Energieausweis wird nun erstellt und Sie erhalten ihn in Kürze per E-Mail.</p>

            <p>Bei Fragen stehen wir Ihnen gerne zur Verfügung.</p>

            <p>Mit freundlichen Grüßen<br>
            Ihr Energieausweis-Team</p>
          </div>
          <div class="footer">
            <p>Diese E-Mail wurde automatisch generiert. Bitte antworten Sie nicht auf diese E-Mail.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    return { subject, html };
  }

  // Generate customer failure email content
  private generateCustomerFailureEmail(certificate: CertificateData, userEmail: string): { subject: string; html: string } {
    const subject = `Zahlungsproblem - Energieausweis ${certificate.order_number}`;

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Zahlungsproblem</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #dc2626; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
          .retry-button { background-color: #16a34a; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 15px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Zahlungsproblem</h1>
          </div>
          <div class="content">
            <p>Leider konnte Ihre Zahlung für den Energieausweis nicht verarbeitet werden.</p>

            <div class="details">
              <h3>Bestelldetails:</h3>
              <p><strong>Bestellnummer:</strong> ${certificate.order_number}</p>
              <p><strong>Bestelldatum:</strong> ${new Date(certificate.created_at).toLocaleDateString('de-DE')}</p>
            </div>

            <p>Mögliche Gründe für das Zahlungsproblem:</p>
            <ul>
              <li>Unzureichende Deckung auf dem Konto</li>
              <li>Falsche Kartendaten</li>
              <li>Technisches Problem bei der Zahlungsabwicklung</li>
            </ul>

            <p>Sie können die Zahlung jederzeit erneut versuchen:</p>
            <a href="${Deno.env.get('SITE_URL') || 'https://k2-energieausweis.de'}/meine-zertifikate" class="retry-button">Zahlung erneut versuchen</a>

            <p>Bei anhaltenden Problemen kontaktieren Sie uns bitte.</p>

            <p>Mit freundlichen Grüßen<br>
            Ihr Energieausweis-Team</p>
          </div>
          <div class="footer">
            <p>Diese E-Mail wurde automatisch generiert. Bitte antworten Sie nicht auf diese E-Mail.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    return { subject, html };
  }

  // Generate admin success email content
  private generateAdminSuccessEmail(certificate: CertificateData, userEmail: string): { subject: string; html: string } {
    const certificateTypeMap: Record<string, string> = {
      'WG/V': 'Wohngebäude Verbrauchsausweis',
      'WG/B': 'Wohngebäude Bedarfsausweis',
      'NWG/V': 'Nichtwohngebäude Verbrauchsausweis'
    };

    const certificateTypeName = certificateTypeMap[certificate.certificate_type] || certificate.certificate_type;
    const buildingAddress = certificate.objektdaten?.strasse && certificate.objektdaten?.hausnummer
      ? `${certificate.objektdaten.strasse} ${certificate.objektdaten.hausnummer}, ${certificate.objektdaten.plz} ${certificate.objektdaten.ort}`
      : 'Nicht verfügbar';

    const subject = `Neue Bestellung - Energieausweis ${certificate.order_number}`;

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Neue Bestellung</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 700px; margin: 0 auto; padding: 20px; }
          .header { background-color: #1f2937; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
          table { width: 100%; border-collapse: collapse; margin: 10px 0; }
          th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
          th { background-color: #f8f9fa; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Neue Energieausweis-Bestellung</h1>
          </div>
          <div class="content">
            <p>Eine neue Bestellung ist eingegangen und wurde erfolgreich bezahlt.</p>

            <div class="details">
              <h3>Bestellinformationen:</h3>
              <table>
                <tr><th>Bestellnummer</th><td>${certificate.order_number}</td></tr>
                <tr><th>Zertifikat-ID</th><td>${certificate.id}</td></tr>
                <tr><th>Energieausweis-Typ</th><td>${certificateTypeName}</td></tr>
                <tr><th>Kunde E-Mail</th><td>${userEmail}</td></tr>
                <tr><th>Bestelldatum</th><td>${new Date(certificate.created_at).toLocaleDateString('de-DE', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}</td></tr>
                <tr><th>Zahlungsstatus</th><td>${certificate.payment_status}</td></tr>
              </table>
            </div>

            <div class="details">
              <h3>Immobilieninformationen:</h3>
              <table>
                <tr><th>Adresse</th><td>${buildingAddress}</td></tr>
                ${certificate.objektdaten?.baujahr ? `<tr><th>Baujahr</th><td>${certificate.objektdaten.baujahr}</td></tr>` : ''}
                ${certificate.objektdaten?.wohnflaeche ? `<tr><th>Wohnfläche</th><td>${certificate.objektdaten.wohnflaeche} m²</td></tr>` : ''}
                ${certificate.objektdaten?.anzahlWohneinheiten ? `<tr><th>Anzahl Wohneinheiten</th><td>${certificate.objektdaten.anzahlWohneinheiten}</td></tr>` : ''}
              </table>
            </div>

            <p><strong>Nächste Schritte:</strong></p>
            <ul>
              <li>Energieausweis erstellen</li>
              <li>Qualitätsprüfung durchführen</li>
              <li>Energieausweis an Kunden senden</li>
            </ul>

            <p>Bestellung im Admin-Dashboard anzeigen: <a href="${Deno.env.get('SITE_URL') || 'https://k2-energieausweis.de'}/admin">Admin-Dashboard</a></p>
          </div>
          <div class="footer">
            <p>Diese E-Mail wurde automatisch generiert.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    return { subject, html };
  }

  // Generate admin failure email content
  private generateAdminFailureEmail(certificate: CertificateData, userEmail: string): { subject: string; html: string } {
    const subject = `Zahlungsfehlschlag - Energieausweis ${certificate.order_number}`;

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Zahlungsfehlschlag</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #dc2626; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
          table { width: 100%; border-collapse: collapse; margin: 10px 0; }
          th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
          th { background-color: #f8f9fa; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Zahlungsfehlschlag</h1>
          </div>
          <div class="content">
            <p>Eine Zahlung für eine Energieausweis-Bestellung ist fehlgeschlagen.</p>

            <div class="details">
              <h3>Bestellinformationen:</h3>
              <table>
                <tr><th>Bestellnummer</th><td>${certificate.order_number}</td></tr>
                <tr><th>Zertifikat-ID</th><td>${certificate.id}</td></tr>
                <tr><th>Kunde E-Mail</th><td>${userEmail}</td></tr>
                <tr><th>Bestelldatum</th><td>${new Date(certificate.created_at).toLocaleDateString('de-DE')}</td></tr>
                <tr><th>Zahlungsstatus</th><td>${certificate.payment_status}</td></tr>
              </table>
            </div>

            <p><strong>Erforderliche Maßnahmen:</strong></p>
            <ul>
              <li>Kunde wurde über den Zahlungsfehlschlag informiert</li>
              <li>Kunde kann Zahlung erneut versuchen</li>
              <li>Bei wiederholten Problemen Kunde kontaktieren</li>
            </ul>

            <p>Bestellung im Admin-Dashboard anzeigen: <a href="${Deno.env.get('SITE_URL') || 'https://k2-energieausweis.de'}/admin">Admin-Dashboard</a></p>
          </div>
          <div class="footer">
            <p>Diese E-Mail wurde automatisch generiert.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    return { subject, html };
  }

  // Send email using Resend
  private async sendEmail(to: string, subject: string, html: string): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      const { data, error } = await this.resend.emails.send({
        from: this.fromEmail,
        to: [to],
        subject,
        html,
      });

      if (error) {
        console.error('Resend error:', error);
        return { success: false, error: error.message || 'Unknown Resend error' };
      }

      return { success: true, messageId: data?.id };
    } catch (error) {
      console.error('Email sending error:', error);
      return { success: false, error: error.message || 'Unknown email sending error' };
    }
  }

  // Send customer success notification
  public async sendCustomerSuccessNotification(certificateId: string): Promise<boolean> {
    try {
      const data = await this.getCertificateData(certificateId);
      if (!data) {
        console.error('Could not fetch certificate data for customer success notification');
        return false;
      }

      const { certificate, userEmail } = data;
      const { subject, html } = this.generateCustomerSuccessEmail(certificate, userEmail);

      // Log email attempt
      const logId = await this.logEmail({
        certificate_id: certificateId,
        recipient_email: userEmail,
        email_type: 'customer_success',
        status: 'pending'
      });

      // Send email
      const result = await this.sendEmail(userEmail, subject, html);

      // Update log
      if (logId) {
        await this.updateEmailLog(logId, {
          status: result.success ? 'sent' : 'failed',
          sent_at: result.success ? new Date().toISOString() : undefined,
          error_message: result.error,
          resend_message_id: result.messageId
        });
      }

      if (!result.success) {
        console.error('Failed to send customer success email:', result.error);
        return false;
      }

      console.log(`Customer success email sent to ${userEmail} for certificate ${certificateId}`);
      return true;
    } catch (error) {
      console.error('Error sending customer success notification:', error);
      return false;
    }
  }

  // Send customer failure notification
  public async sendCustomerFailureNotification(certificateId: string): Promise<boolean> {
    try {
      const data = await this.getCertificateData(certificateId);
      if (!data) {
        console.error('Could not fetch certificate data for customer failure notification');
        return false;
      }

      const { certificate, userEmail } = data;
      const { subject, html } = this.generateCustomerFailureEmail(certificate, userEmail);

      // Log email attempt
      const logId = await this.logEmail({
        certificate_id: certificateId,
        recipient_email: userEmail,
        email_type: 'customer_failure',
        status: 'pending'
      });

      // Send email
      const result = await this.sendEmail(userEmail, subject, html);

      // Update log
      if (logId) {
        await this.updateEmailLog(logId, {
          status: result.success ? 'sent' : 'failed',
          sent_at: result.success ? new Date().toISOString() : undefined,
          error_message: result.error,
          resend_message_id: result.messageId
        });
      }

      if (!result.success) {
        console.error('Failed to send customer failure email:', result.error);
        return false;
      }

      console.log(`Customer failure email sent to ${userEmail} for certificate ${certificateId}`);
      return true;
    } catch (error) {
      console.error('Error sending customer failure notification:', error);
      return false;
    }
  }

  // Send admin success notification
  public async sendAdminSuccessNotification(certificateId: string): Promise<boolean> {
    try {
      const data = await this.getCertificateData(certificateId);
      if (!data) {
        console.error('Could not fetch certificate data for admin success notification');
        return false;
      }

      const { certificate, userEmail } = data;
      const { subject, html } = this.generateAdminSuccessEmail(certificate, userEmail);

      // Log email attempt
      const logId = await this.logEmail({
        certificate_id: certificateId,
        recipient_email: this.adminEmail,
        email_type: 'admin_success',
        status: 'pending'
      });

      // Send email
      const result = await this.sendEmail(this.adminEmail, subject, html);

      // Update log
      if (logId) {
        await this.updateEmailLog(logId, {
          status: result.success ? 'sent' : 'failed',
          sent_at: result.success ? new Date().toISOString() : undefined,
          error_message: result.error,
          resend_message_id: result.messageId
        });
      }

      if (!result.success) {
        console.error('Failed to send admin success email:', result.error);
        return false;
      }

      console.log(`Admin success email sent to ${this.adminEmail} for certificate ${certificateId}`);
      return true;
    } catch (error) {
      console.error('Error sending admin success notification:', error);
      return false;
    }
  }

  // Send admin failure notification
  public async sendAdminFailureNotification(certificateId: string): Promise<boolean> {
    try {
      const data = await this.getCertificateData(certificateId);
      if (!data) {
        console.error('Could not fetch certificate data for admin failure notification');
        return false;
      }

      const { certificate, userEmail } = data;
      const { subject, html } = this.generateAdminFailureEmail(certificate, userEmail);

      // Log email attempt
      const logId = await this.logEmail({
        certificate_id: certificateId,
        recipient_email: this.adminEmail,
        email_type: 'admin_failure',
        status: 'pending'
      });

      // Send email
      const result = await this.sendEmail(this.adminEmail, subject, html);

      // Update log
      if (logId) {
        await this.updateEmailLog(logId, {
          status: result.success ? 'sent' : 'failed',
          sent_at: result.success ? new Date().toISOString() : undefined,
          error_message: result.error,
          resend_message_id: result.messageId
        });
      }

      if (!result.success) {
        console.error('Failed to send admin failure email:', result.error);
        return false;
      }

      console.log(`Admin failure email sent to ${this.adminEmail} for certificate ${certificateId}`);
      return true;
    } catch (error) {
      console.error('Error sending admin failure notification:', error);
      return false;
    }
  }

  // Send all notifications for payment success
  public async sendPaymentSuccessNotifications(certificateId: string): Promise<{ customerSent: boolean; adminSent: boolean }> {
    const customerSent = await this.sendCustomerSuccessNotification(certificateId);
    const adminSent = await this.sendAdminSuccessNotification(certificateId);

    return { customerSent, adminSent };
  }

  // Send all notifications for payment failure
  public async sendPaymentFailureNotifications(certificateId: string): Promise<{ customerSent: boolean; adminSent: boolean }> {
    const customerSent = await this.sendCustomerFailureNotification(certificateId);
    const adminSent = await this.sendAdminFailureNotification(certificateId);

    return { customerSent, adminSent };
  }
}

// Helper function to validate certificate ID format
function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

// Helper function to safely update certificate payment status
async function updateCertificatePaymentStatus(
  supabase: any,
  certificateId: string,
  paymentStatus: string,
  sessionId: string,
  requestId: string
): Promise<{ success: boolean; orderNumber?: string; error?: string }> {
  try {
    // First, get the current certificate to check if order_number exists
    const { data: currentCert, error: fetchError } = await supabase
      .from('energieausweise')
      .select('order_number, payment_status')
      .eq('id', certificateId)
      .single();

    if (fetchError) {
      console.error(`❌ Error fetching certificate ${certificateId} [${requestId}]:`, fetchError);
      return { success: false, error: `Failed to fetch certificate: ${fetchError.message}` };
    }

    // Check if payment was already processed to prevent double processing
    if (paymentStatus === 'paid' && currentCert?.payment_status === 'paid') {
      console.log(`⚠️ Certificate ${certificateId} is already marked as paid - skipping duplicate processing [${requestId}]`);
      return { success: true, orderNumber: currentCert.order_number };
    }

    // Generate order_number if it doesn't exist and payment is successful
    const orderNumber = currentCert?.order_number ||
      (paymentStatus === 'paid' ? `EA-${certificateId.slice(-8).toUpperCase()}` : undefined);

    // Update payment status in database
    const updateData: any = {
      payment_status: paymentStatus,
      stripe_checkout_session_id: sessionId,
      updated_at: new Date().toISOString(),
    };

    if (orderNumber) {
      updateData.order_number = orderNumber;
    }

    const { error: updateError } = await supabase
      .from('energieausweise')
      .update(updateData)
      .eq('id', certificateId)
      .select();

    if (updateError) {
      console.error(`❌ Error updating payment status for certificate ${certificateId} [${requestId}]:`, updateError);
      return { success: false, error: `Failed to update payment status: ${updateError.message}` };
    }

    console.log(`✅ Successfully updated payment status to '${paymentStatus}' for certificate: ${certificateId} [${requestId}]`);
    if (orderNumber) {
      console.log(`✅ Order number set to: ${orderNumber} [${requestId}]`);
    }

    return { success: true, orderNumber };

  } catch (error) {
    console.error(`❌ Unexpected error updating certificate ${certificateId} [${requestId}]:`, error);
    return { success: false, error: error.message || 'Unexpected error' };
  }
}

/**
 * Main webhook handler function
 */
Deno.serve(async (req) => {
  // Generate unique request ID for tracking
  const requestId = crypto.randomUUID();
  const startTime = Date.now();

  console.log(`\n🚀 === WEBHOOK REQUEST START [${requestId}] ===`);
  console.log(`Request method: ${req.method}`);
  console.log(`Request URL: ${req.url}`);
  console.log(`Timestamp: ${new Date().toISOString()}`);

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    console.log(`✅ CORS preflight handled [${requestId}]`);
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }

  // Validate request method
  if (req.method !== 'POST') {
    console.log(`❌ Invalid method ${req.method} [${requestId}]`);
    return new Response(
      JSON.stringify({
        error: 'Method not allowed',
        allowed_methods: ['POST'],
        request_id: requestId
      }),
      {
        status: 405,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }

  try {
    // Validate environment variables
    const stripeSecretKey = Deno.env.get("STRIPE_SECRET_KEY");
    const webhookSecret = Deno.env.get("STRIPE_WEBHOOK_SECRET");
    const supabaseUrl = Deno.env.get("SUPABASE_URL");
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");

    if (!stripeSecretKey || !webhookSecret || !supabaseUrl || !supabaseServiceKey) {
      const missingVars = [];
      if (!stripeSecretKey) missingVars.push("STRIPE_SECRET_KEY");
      if (!webhookSecret) missingVars.push("STRIPE_WEBHOOK_SECRET");
      if (!supabaseUrl) missingVars.push("SUPABASE_URL");
      if (!supabaseServiceKey) missingVars.push("SUPABASE_SERVICE_ROLE_KEY");

      console.error(`❌ Missing environment variables: ${missingVars.join(", ")} [${requestId}]`);
      return new Response(
        JSON.stringify({
          error: "Configuration Error",
          message: `Missing required environment variables: ${missingVars.join(", ")}`,
          request_id: requestId
        }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Get signature from headers
    const signature = req.headers.get("stripe-signature");

    if (!signature) {
      console.error(`❌ Missing Stripe signature header [${requestId}]`);
      return new Response(
        JSON.stringify({
          error: "Missing Stripe signature",
          message: "Webhook signature verification failed - no signature header found",
          request_id: requestId
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Get the raw body for signature verification
    const rawBody = await req.text();

    console.log(`[${requestId}] Raw body for verification :`, rawBody);

    // Initialize Stripe with secret key
    const stripe = createStripeInstance(stripeSecretKey);

    // Create SubtleCryptoProvider for async webhook verification in Deno
    const cryptoProvider = Stripe.createSubtleCryptoProvider();

    // Verify the webhook signature
    let event: Stripe.Event;

    try {
      // Use the async version of constructEvent which is required for Deno
      event = await stripe.webhooks.constructEventAsync(
        rawBody,
        signature,
        webhookSecret,
        undefined,
        cryptoProvider
      );

      console.log(`✅ Signature verification successful [${requestId}]`);
    } catch (err) {
      console.error(`❌ Webhook signature verification failed [${requestId}]: ${err.message}`);

      return new Response(
        JSON.stringify({
          error: "Webhook signature verification failed",
          message: err.message,
          request_id: requestId
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Log the verified event details
    console.log(`Event ID: ${event.id} [${requestId}]`);
    console.log(`Event Type: ${event.type} [${requestId}]`);
    console.log(`Event Created: ${new Date(event.created * 1000).toISOString()} [${requestId}]`);

    // Initialize Supabase client
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    console.log("✅ Supabase client initialized");

    // Initialize email service with shared Supabase client (optional - don't fail if this fails)
    let emailService: EmailService | null = null;
    try {
      emailService = new EmailService(supabase);
      console.log("✅ Email service initialized successfully");
    } catch (emailError) {
      console.warn('⚠️ Email service initialization failed:', emailError);
      // Continue without email service - webhook should still work for payment processing
    }

    // Handle different event types
    try {
      switch (event.type) {
        case 'payment_intent.created': {
          const paymentIntent = event.data.object as Stripe.PaymentIntent;
          console.log(`ℹ️ Payment intent created [${requestId}]:`);
          console.log(`   Payment ID: ${paymentIntent.id}`);
          console.log(`   Amount: ${paymentIntent.amount / 100} ${paymentIntent.currency.toUpperCase()}`);
          console.log(`   Customer: ${paymentIntent.customer}`);
          console.log(`   Status: ${paymentIntent.status}`);

          // Log for tracking purposes - no action needed for created events
          break;
        }

        case 'payment_intent.succeeded': {
          const paymentIntent = event.data.object as Stripe.PaymentIntent;
          console.log(`✅ Payment intent succeeded [${requestId}]:`);
          console.log(`   Payment ID: ${paymentIntent.id}`);
          console.log(`   Amount: ${paymentIntent.amount / 100} ${paymentIntent.currency.toUpperCase()}`);
          console.log(`   Customer: ${paymentIntent.customer}`);

          // Try to find the certificate ID from metadata
          const certificateId = paymentIntent.metadata?.certificate_id;
          if (certificateId && isValidUUID(certificateId)) {
            console.log(`   Certificate ID: ${certificateId}`);

            // Send success notifications (non-critical)
            if (emailService) {
              try {
                console.log('📧 Sending payment success email notifications for payment intent...');
                const emailResults = await emailService.sendPaymentSuccessNotifications(certificateId);
                console.log('📧 Email notification results for payment intent:', emailResults);
              } catch (emailError) {
                console.error('❌ Error sending email notifications for payment intent:', emailError);
              }
            }
          } else {
            console.warn('⚠️ No valid certificate_id found in payment intent metadata');
          }

          // Note: We primarily handle payment completion via checkout.session.completed
          // This event is logged for completeness but doesn't trigger payment status updates
          break;
        }

        case 'checkout.session.completed': {
          const session = event.data.object as Stripe.Checkout.Session;

          console.log(`✅ Checkout session completed [${requestId}]:`);
          console.log(`   Session ID: ${session.id}`);
          console.log(`   Customer: ${session.customer}`);
          console.log(`   Client reference ID: ${session.client_reference_id}`);
          console.log(`   Payment status: ${session.payment_status}`);

          // Comprehensive session validation
          if (!session.id) {
            console.error(`❌ Invalid session - missing session ID [${requestId}]`);
            throw new Error('Invalid session: missing session ID');
          }

          if (!session.client_reference_id) {
            console.warn(`⚠️ No client_reference_id found in session ${session.id} - cannot update payment status [${requestId}]`);
            break;
          }

          // Validate client_reference_id format (should be UUID)
          if (!isValidUUID(session.client_reference_id)) {
            console.error(`❌ Invalid client_reference_id format: ${session.client_reference_id} [${requestId}]`);
            throw new Error(`Invalid client_reference_id format: ${session.client_reference_id}`);
          }

          // Additional validation: check if payment was actually successful
          if (session.payment_status !== 'paid') {
            console.warn(`⚠️ Session ${session.id} payment status is '${session.payment_status}', not 'paid' - skipping payment update [${requestId}]`);
            break;
          }

          // Validate session mode
          if (session.mode !== 'payment') {
            console.warn(`⚠️ Session ${session.id} mode is '${session.mode}', not 'payment' - skipping payment update [${requestId}]`);
            break;
          }

          try {
            // Use helper function to safely update certificate payment status
            const updateResult = await updateCertificatePaymentStatus(
              supabase,
              session.client_reference_id,
              'paid',
              session.id,
              requestId
            );

            if (!updateResult.success) {
              throw new Error(updateResult.error);
            }

            // If payment was already processed, skip email notifications
            if (updateResult.orderNumber && updateResult.success) {
              console.log(`✅ Payment processing completed for certificate: ${session.client_reference_id} [${requestId}]`);
            } else {
              console.log(`⚠️ Payment was already processed for certificate: ${session.client_reference_id} [${requestId}]`);
              break;
            }

            // Send email notifications (non-critical - don't fail webhook if this fails)
            if (emailService) {
              try {
                console.log('📧 Sending payment success email notifications...');
                const emailResults = await emailService.sendPaymentSuccessNotifications(session.client_reference_id);
                console.log('📧 Email notification results:', emailResults);

                if (!emailResults.customerSent) {
                  console.warn('⚠️ Failed to send customer success email');
                }
                if (!emailResults.adminSent) {
                  console.warn('⚠️ Failed to send admin success email');
                }
              } catch (emailError) {
                console.error('❌ Error sending email notifications:', emailError);
                // Don't fail the webhook if email sending fails - payment was already processed
              }
            } else {
              console.warn('⚠️ Email service not available - skipping email notifications');
            }

          } catch (paymentError) {
            console.error('❌ Error processing payment completion:', paymentError);
            // Re-throw to trigger webhook retry by Stripe
            throw paymentError;
          }

          break;
        }

        case 'charge.succeeded': {
          const charge = event.data.object as Stripe.Charge;
          console.log(`✅ Charge succeeded [${requestId}]:`);
          console.log(`   Charge ID: ${charge.id}`);
          console.log(`   Amount: ${charge.amount / 100} ${charge.currency.toUpperCase()}`);
          console.log(`   Customer: ${charge.customer}`);
          console.log(`   Payment Intent: ${charge.payment_intent}`);

          // Try to find the certificate ID from metadata
          const certificateId = charge.metadata?.certificate_id;
          if (certificateId && isValidUUID(certificateId)) {
            console.log(`   Certificate ID: ${certificateId}`);

            // Send success notifications (non-critical)
            if (emailService) {
              try {
                console.log('📧 Sending payment success email notifications for charge...');
                const emailResults = await emailService.sendPaymentSuccessNotifications(certificateId);
                console.log('📧 Email notification results for charge:', emailResults);
              } catch (emailError) {
                console.error('❌ Error sending email notifications for charge:', emailError);
              }
            }
          } else {
            console.warn('⚠️ No valid certificate_id found in charge metadata');
          }

          // Note: We primarily handle payment completion via checkout.session.completed
          // This event is logged for completeness and provides additional confirmation
          break;
        }
        default:
          // Log unhandled event types
          console.log(`ℹ️ Unhandled event type: ${event.type} [${requestId}]`);
      }
    } catch (err) {
      // Log the error but still return a 200 response to acknowledge receipt
      console.error(`❌ Error processing event type ${event.type} [${requestId}]: ${err.message}`);
      console.error(err.stack);
    }

    // Calculate processing time
    const processingTime = Date.now() - startTime;
    console.log(`✅ Event processed in ${processingTime}ms [${requestId}]`);
    console.log(`🏁 === WEBHOOK REQUEST END [${requestId}] ===\n`);

    // Return a 200 response to acknowledge receipt of the event
    return new Response(
      JSON.stringify({
        received: true,
        event_id: event.id,
        event_type: event.type,
        request_id: requestId
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    // Handle any unexpected errors
    const processingTime = Date.now() - startTime;
    console.error(`❌ Unexpected error [${requestId}]: ${error.message}`);
    console.error(error.stack);
    console.error(`🏁 === WEBHOOK REQUEST END (ERROR) [${requestId}] ===\n`);

    return new Response(
      JSON.stringify({
        error: "Webhook Processing Error",
        message: error.message,
        request_id: requestId,
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});
