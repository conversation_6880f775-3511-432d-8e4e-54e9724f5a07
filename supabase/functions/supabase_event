[740d95a3-65cd-429c-800e-c9188282c607] Raw body for verification (first 500 chars): {
  "id": "evt_3RWIJwBTHOMwD3B81vBGy2NU",
  "object": "event",
  "api_version": "2025-04-30.basil",
  "created": 1749047377,
  "data": {
    "object": {
      "id": "pi_3RWIJwBTHOMwD3B81fKW6Kol",
      "object": "payment_intent",
      "amount": 4900,
      "amount_capturable": 0,
      "amount_details": {
        "tip": {
        }
      },
      "amount_received": 4900,
      "application": null,
      "application_fee_amount": null,
      "automatic_payment_methods": null,
      "canceled_at": null,
      "cancellation_reason": null,
      "capture_method": "automatic",
      "client_secret": "pi_3RWIJwBTHOMwD3B81fKW6Kol_secret_8XyVBihaXaC9xSxjJwiyF6PUK",
      "confirmation_method": "automatic",
      "created": 1749047376,
      "currency": "eur",
      "customer": null,
      "description": null,
      "last_payment_error": null,
      "latest_charge": "ch_3RWIJwBTHOMwD3B81Gtuz4kO",
      "livemode": false,
      "metadata": {
        "order_number": "EA-C043D1F3",
        "certificate_id": "3253b5fa-bb1a-499e-95ab-636bc043d1f3"
      },
      "next_action": null,
      "on_behalf_of": null,
      "payment_method": "pm_1RWIJvBTHOMwD3B8LILLFcXU",
      "payment_method_configuration_details": null,
      "payment_method_options": {
        "card": {
          "installments": null,
          "mandate_options": null,
          "network": null,
          "request_three_d_secure": "automatic"
        }
      },
      "payment_method_types": [
        "card"
      ],
      "processing": null,
      "receipt_email": null,
      "review": null,
      "setup_future_usage": null,
      "shipping": null,
      "source": null,
      "statement_descriptor": null,
      "statement_descriptor_suffix": null,
      "status": "succeeded",
      "transfer_data": null,
      "transfer_group": null
    }
  },
  "livemode": false,
  "pending_webhooks": 3,
  "request": {
    "id": null,
    "idempotency_key": "9f4bf1a9-862a-4bc9-84cd-671dc84f3719"
  },
  "type": "payment_intent.succeeded"
}
