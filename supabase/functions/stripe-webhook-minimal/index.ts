// Import required dependencies for Deno runtime
import Stripe from "https://esm.sh/stripe@15.6.0?target=deno";

// CORS headers to allow cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, stripe-signature',
  'Access-Control-Allow-Methods': 'POST, OPTIONS'
};

/**
 * Create a new Stripe instance with the provided secret key
 * This ensures a fresh instance for each request to avoid shared state issues
 */
function createStripeInstance(secretKey: string): Stripe {
  return new Stripe(secretKey, {
    apiVersion: '2024-11-20', // Using same API version as working supa.ts
  });
}

/**
 * Process a payment intent succeeded event
 * This is where you would implement your business logic for successful payments
 */
async function handlePaymentIntentSucceeded(event: Stripe.Event, requestId: string): Promise<void> {
  const paymentIntent = event.data.object as Stripe.PaymentIntent;
  
  console.log(`✅ Payment succeeded [${requestId}]:`);
  console.log(`   Payment ID: ${paymentIntent.id}`);
  console.log(`   Amount: ${paymentIntent.amount / 100} ${paymentIntent.currency.toUpperCase()}`);
  console.log(`   Customer: ${paymentIntent.customer}`);
  
  // TODO: Implement your business logic here
  // For example:
  // 1. Update order status in your database
  // 2. Send confirmation email to customer
  // 3. Trigger fulfillment process
}

/**
 * Process a payment method attached event
 */
async function handlePaymentMethodAttached(event: Stripe.Event, requestId: string): Promise<void> {
  const paymentMethod = event.data.object as Stripe.PaymentMethod;
  
  console.log(`✅ Payment method attached [${requestId}]:`);
  console.log(`   Payment Method ID: ${paymentMethod.id}`);
  console.log(`   Type: ${paymentMethod.type}`);
  console.log(`   Customer: ${paymentMethod.customer}`);
  
  // TODO: Implement your business logic for payment method attachment
}

/**
 * Main webhook handler function
 */
Deno.serve(async (req) => {
  // Generate unique request ID for tracking
  const requestId = crypto.randomUUID();
  const startTime = Date.now();

  console.log(`\n🚀 === WEBHOOK REQUEST START [${requestId}] ===`);
  console.log(`Request method: ${req.method}`);
  console.log(`Request URL: ${req.url}`);
  console.log(`Timestamp: ${new Date().toISOString()}`);

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    console.log(`✅ CORS preflight handled [${requestId}]`);
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }

  // Validate request method
  if (req.method !== 'POST') {
    console.log(`❌ Invalid method ${req.method} [${requestId}]`);
    return new Response(
      JSON.stringify({ 
        error: 'Method not allowed', 
        allowed_methods: ['POST'],
        request_id: requestId 
      }),
      { 
        status: 405, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }

  try {
    // Validate environment variables (using same names as working supa.ts)
    const stripeSecretKey = Deno.env.get("STRIPE_API_KEY");
    const webhookSecret = Deno.env.get("STRIPE_WEBHOOK_SECRET");

    if (!stripeSecretKey || !webhookSecret) {
      const missingVars: string[] = [];
      if (!stripeSecretKey) missingVars.push("STRIPE_API_KEY");
      if (!webhookSecret) missingVars.push("STRIPE_WEBHOOK_SECRET");
      
      console.error(`❌ Missing environment variables: ${missingVars.join(", ")} [${requestId}]`);
      return new Response(
        JSON.stringify({
          error: "Configuration Error",
          message: `Missing required environment variables: ${missingVars.join(", ")}. Expected: STRIPE_API_KEY and STRIPE_WEBHOOK_SECRET`,
          request_id: requestId
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Get signature from headers (case-sensitive header name)
    const signature = req.headers.get("Stripe-Signature");

    // Debug: Log all headers to see what we're receiving
    console.log(`[${requestId}] All request headers:`);
    for (const [key, value] of req.headers.entries()) {
      console.log(`  ${key}: ${value}`);
    }

    if (!signature) {
      console.error(`❌ Missing Stripe signature header [${requestId}]`);
      console.error(`Available headers: ${Array.from(req.headers.keys()).join(', ')}`);
      return new Response(
        JSON.stringify({
          error: "Missing Stripe signature",
          message: "Webhook signature verification failed - no signature header found",
          request_id: requestId,
          available_headers: Array.from(req.headers.keys())
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    console.log(`[${requestId}] Stripe signature found: ${signature.substring(0, 50)}...`);


    // Get the raw body for signature verification
    console.log(`[${requestId}] Reading request body...`);
    const rawBody = await req.text();

    console.log(`[${requestId}] Raw body length: ${rawBody.length} characters`);
    console.log(`[${requestId}] Raw body for verification (first 200 chars):`, rawBody.substring(0, 200));

    // Check if body is valid JSON
    try {
      const jsonBody = JSON.parse(rawBody);
      console.log(`[${requestId}] Body is valid JSON, event type: ${jsonBody.type || 'unknown'}`);
    } catch (jsonErr) {
      console.error(`[${requestId}] Body is not valid JSON: ${jsonErr.message}`);
    }
    // Initialize Stripe with secret key
    const stripe = createStripeInstance(stripeSecretKey);
    
    // Create SubtleCryptoProvider for async webhook verification in Deno
    const cryptoProvider = Stripe.createSubtleCryptoProvider();

    // Verify the webhook signature
    let event: Stripe.Event;
    
    try {
      console.log(`[${requestId}] Attempting signature verification...`);
      console.log(`[${requestId}] Webhook secret length: ${webhookSecret.length}`);
      console.log(`[${requestId}] Signature length: ${signature.length}`);
      console.log(`[${requestId}] Body length: ${rawBody.length}`);

      // Use the async version of constructEvent which is required for Deno
      event = await stripe.webhooks.constructEventAsync(
        rawBody,
        signature,
        webhookSecret,
        undefined,
        cryptoProvider
      );

      console.log(`✅ Signature verification successful [${requestId}]`);
    } catch (err) {
      console.error(`❌ Webhook signature verification failed [${requestId}]: ${err.message}`);
      console.error(`[${requestId}] Error details:`, err);
      console.error(`[${requestId}] Signature: ${signature}`);
      console.error(`[${requestId}] Webhook secret (first 10 chars): ${webhookSecret.substring(0, 10)}...`);

      return new Response(
        JSON.stringify({
          error: "Webhook signature verification failed",
          message: err.message,
          request_id: requestId,
          debug_info: {
            signature_length: signature.length,
            body_length: rawBody.length,
            webhook_secret_length: webhookSecret.length
          }
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Log the verified event details
    console.log(`Event ID: ${event.id} [${requestId}]`);
    console.log(`Event Type: ${event.type} [${requestId}]`);
    console.log(`Event Created: ${new Date(event.created * 1000).toISOString()} [${requestId}]`);

    // Handle different event types
    try {
      switch (event.type) {
        case 'payment_intent.succeeded':
          await handlePaymentIntentSucceeded(event, requestId);
          break;
          
        case 'payment_method.attached':
          await handlePaymentMethodAttached(event, requestId);
          break;
          
        case 'checkout.session.completed':
          console.log(`✅ Checkout session completed [${requestId}]`);
          const session = event.data.object as Stripe.Checkout.Session;
          console.log(`   Session ID: ${session.id}`);
          console.log(`   Customer: ${session.customer}`);
          // TODO: Implement checkout session completion logic
          break;
          
        default:
          // Log unhandled event types
          console.log(`ℹ️ Unhandled event type: ${event.type} [${requestId}]`);
      }
    } catch (err) {
      // Log the error but still return a 200 response to acknowledge receipt
      console.error(`❌ Error processing event type ${event.type} [${requestId}]: ${err.message}`);
      console.error(err.stack);
    }

    // Calculate processing time
    const processingTime = Date.now() - startTime;
    console.log(`✅ Event processed in ${processingTime}ms [${requestId}]`);
    console.log(`🏁 === WEBHOOK REQUEST END [${requestId}] ===\n`);

    // Return a 200 response to acknowledge receipt of the event
    return new Response(
      JSON.stringify({
        received: true,
        event_id: event.id,
        event_type: event.type,
        request_id: requestId
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    // Handle any unexpected errors
    const processingTime = Date.now() - startTime;
    console.error(`❌ Unexpected error [${requestId}]: ${error.message}`);
    console.error(error.stack);
    console.error(`🏁 === WEBHOOK REQUEST END (ERROR) [${requestId}] ===\n`);

    return new Response(
      JSON.stringify({
        error: "Webhook Processing Error",
        message: error.message,
        request_id: requestId,
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});